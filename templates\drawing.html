{% extends "base.html" %}

{% block content %}
<div class="rtl-text">
    <h1 class="mb-4">واجهة الرسم الهندسي</h1>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5>أدوات الرسم</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 mb-3">
                        <button id="wallTool" class="btn btn-outline-primary active">جدار</button>
                        <button id="doorTool" class="btn btn-outline-primary">باب</button>
                        <button id="windowTool" class="btn btn-outline-primary">نافذة</button>
                    </div>
                    <div class="d-grid gap-2">
                        <button id="clearCanvas" class="btn btn-outline-danger">مسح الرسم</button>
                        <button id="saveDrawing" class="btn btn-success">حفظ الرسم</button>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>خصائص الشكل</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="shapeWidth" class="form-label">العرض (متر)</label>
                        <input type="number" id="shapeWidth" class="form-control" value="1" min="0.1" step="0.1">
                    </div>
                    <div class="mb-3">
                        <label for="shapeHeight" class="form-label">الارتفاع (متر)</label>
                        <input type="number" id="shapeHeight" class="form-control" value="2" min="0.1" step="0.1">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5>لوحة الرسم</h5>
                </div>
                <div class="card-body">
                    <canvas id="drawingCanvas" width="800" height="600" style="border:1px solid #ccc; background-color: #f8f9fa;"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const canvas = new fabric.Canvas('drawingCanvas');
    let currentTool = 'wall';
    let currentShape = null;
    let isDrawing = false;
    
    // Set canvas background to grid
    function drawGrid() {
        const gridSize = 20;
        const width = canvas.width;
        const height = canvas.height;
        
        canvas.clear();
        canvas.backgroundColor = '#f8f9fa';
        
        // Draw grid lines
        for (let i = 0; i < width; i += gridSize) {
            canvas.add(new fabric.Line([i, 0, i, height], {
                stroke: '#ddd',
                selectable: false
            }));
        }
        
        for (let i = 0; i < height; i += gridSize) {
            canvas.add(new fabric.Line([0, i, width, i], {
                stroke: '#ddd',
                selectable: false
            }));
        }
    }
    
    drawGrid();
    
    // Tool selection
    document.getElementById('wallTool').addEventListener('click', () => setActiveTool('wall'));
    document.getElementById('doorTool').addEventListener('click', () => setActiveTool('door'));
    document.getElementById('windowTool').addEventListener('click', () => setActiveTool('window'));
    
    function setActiveTool(tool) {
        currentTool = tool;
        
        // Update button states
        document.getElementById('wallTool').classList.remove('active');
        document.getElementById('doorTool').classList.remove('active');
        document.getElementById('windowTool').classList.remove('active');
        document.getElementById(tool + 'Tool').classList.add('active');
    }
    
    // Canvas mouse events
    canvas.on('mouse:down', function(options) {
        if (currentTool === 'wall') {
            isDrawing = true;
            const pointer = canvas.getPointer(options.e);
            currentShape = new fabric.Line([pointer.x, pointer.y, pointer.x, pointer.y], {
                stroke: '#333',
                strokeWidth: 5,
                selectable: false
            });
            canvas.add(currentShape);
        } else {
            // For door/window
            const pointer = canvas.getPointer(options.e);
            const width = parseFloat(document.getElementById('shapeWidth').value) * 20;
            const height = parseFloat(document.getElementById('shapeHeight').value) * 20;
            
            let shape;
            if (currentTool === 'door') {
                shape = new fabric.Rect({
                    left: pointer.x - width/2,
                    top: pointer.y - height/2,
                    width: width,
                    height: height,
                    fill: '#8B4513',
                    stroke: '#333',
                    strokeWidth: 1,
                    rx: 5,
                    ry: 5
                });
            } else { // window
                shape = new fabric.Rect({
                    left: pointer.x - width/2,
                    top: pointer.y - height/2,
                    width: width,
                    height: height,
                    fill: '#87CEEB',
                    stroke: '#333',
                    strokeWidth: 1,
                    strokeDashArray: [5, 5]
                });
            }
            canvas.add(shape);
        }
    });
    
    canvas.on('mouse:move', function(options) {
        if (isDrawing && currentShape && currentTool === 'wall') {
            const pointer = canvas.getPointer(options.e);
            currentShape.set({
                x2: pointer.x,
                y2: pointer.y
            });
            canvas.renderAll();
        }
    });
    
    canvas.on('mouse:up', function() {
        if (isDrawing && currentTool === 'wall') {
            isDrawing = false;
            currentShape = null;
        }
    });
    
    // Clear canvas
    document.getElementById('clearCanvas').addEventListener('click', drawGrid);
    
    // Save drawing
    document.getElementById('saveDrawing').addEventListener('click', function() {
        const drawingData = JSON.stringify(canvas);
        // In a real app, we would send this to the server
        alert('تم حفظ الرسم بنجاح!');
        console.log('Drawing data:', drawingData);
    });
});
</script>
{% endblock %}