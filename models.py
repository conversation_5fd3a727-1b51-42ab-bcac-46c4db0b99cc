from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from extensions import db

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # engineer/supervisor/client
    projects = db.relationship('Project', backref='user', lazy=True)

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(200))
    history = db.Column(db.Date)
    description = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.<PERSON>('user.id'), nullable=False)
    drawings = db.relationship('Drawing', backref='project', lazy=True)
    calculations = db.relationship('Calculation', backref='project', lazy=True)

class Drawing(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    drawing_data = db.Column(db.JSON, nullable=False)
    creation_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Calculation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    calculation_type = db.Column(db.String(50), nullable=False)  # e.g., area, perimeter
    value = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=False)  # e.g., m², m