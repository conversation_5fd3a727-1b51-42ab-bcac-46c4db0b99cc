{% extends "base.html" %}

{% block content %}
<div class="rtl-text">
    <h1 class="mb-4">إدارة المشاريع</h1>
    
    <button type="button" class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#addProjectModal">
        إضافة مشروع جديد
    </button>
    
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>اسم المشروع</th>
                    <th>الموقع</th>
                    <th>التاريخ</th>
                    <th>الوصف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for project in projects %}
                <tr>
                    <td>{{ project.name }}</td>
                    <td>{{ project.location }}</td>
                    <td>{{ project.history }}</td>
                    <td>{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</td>
                    <td>
                        <button class="btn btn-sm btn-warning edit-project" 
                                data-id="{{ project.id }}"
                                data-name="{{ project.name }}"
                                data-location="{{ project.location }}"
                                data-history="{{ project.history }}"
                                data-description="{{ project.description }}">
                            تعديل
                        </button>
                        <button class="btn btn-sm btn-danger delete-project" data-id="{{ project.id }}">حذف</button>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="5" class="text-center">لا توجد مشاريع</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Add Project Modal -->
<div class="modal fade" id="addProjectModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مشروع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addProjectForm" method="POST" action="{{ url_for('add_project') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="projectName" class="form-label">اسم المشروع</label>
                        <input type="text" class="form-control" id="projectName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="projectLocation" class="form-label">الموقع</label>
                        <input type="text" class="form-control" id="projectLocation" name="location">
                    </div>
                    <div class="mb-3">
                        <label for="projectHistory" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="projectHistory" name="history">
                    </div>
                    <div class="mb-3">
                        <label for="projectDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="projectDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Project Modal -->
<div class="modal fade" id="editProjectModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المشروع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProjectForm" method="POST" action="{{ url_for('edit_project') }}">
                <input type="hidden" id="editProjectId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editProjectName" class="form-label">اسم المشروع</label>
                        <input type="text" class="form-control" id="editProjectName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectLocation" class="form-label">الموقع</label>
                        <input type="text" class="form-control" id="editProjectLocation" name="location">
                    </div>
                    <div class="mb-3">
                        <label for="editProjectHistory" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="editProjectHistory" name="history">
                    </div>
                    <div class="mb-3">
                        <label for="editProjectDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="editProjectDescription" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit project button handler
    document.querySelectorAll('.edit-project').forEach(button => {
        button.addEventListener('click', function() {
            const projectId = this.getAttribute('data-id');
            const projectName = this.getAttribute('data-name');
            const projectLocation = this.getAttribute('data-location');
            const projectHistory = this.getAttribute('data-history');
            const projectDescription = this.getAttribute('data-description');
            
            document.getElementById('editProjectId').value = projectId;
            document.getElementById('editProjectName').value = projectName;
            document.getElementById('editProjectLocation').value = projectLocation;
            document.getElementById('editProjectHistory').value = projectHistory;
            document.getElementById('editProjectDescription').value = projectDescription;
            
            // Show the modal
            const editModal = new bootstrap.Modal(document.getElementById('editProjectModal'));
            editModal.show();
        });
    });
});
</script>
{% endblock %}