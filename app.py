from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_babel import Babel, gettext
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from extensions import db

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///site.db'
app.config['BABEL_DEFAULT_LOCALE'] = 'ar'
app.config['BABEL_TRANSLATION_DIRECTORIES'] = 'translations'

db.init_app(app)

# Import models after db initialization
from models import User, Project, Drawing, Calculation
babel = Babel(app)

# Create database tables within application context
with app.app_context():
    try:
        db.create_all()
        print("Database tables created successfully")
    except Exception as e:
        print(f"Error creating database tables: {str(e)}")

@app.route('/health')
def health_check():
    return jsonify(status='ok'), 200

@app.route('/')
def home():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login():
    email = request.form.get('email')
    password = request.form.get('password')
    
    user = User.query.filter_by(email=email).first()
    
    if user and check_password_hash(user.password, password):
        session['user_id'] = user.id
        session['user_name'] = user.name
        session['user_role'] = user.role
        return redirect(url_for('dashboard'))
    else:
        flash('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'danger')
        return redirect(url_for('home'))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('home'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    
    # For demo purposes - in real app, fetch actual data
    return render_template('dashboard.html',
                           user={'name': session['user_name']},
                           project_count=Project.query.filter_by(user_id=session['user_id']).count(),
                           drawing_count=0,
                           calculation_count=0,
                           model_count=0,
                           recent_projects=Project.query.filter_by(user_id=session['user_id']).order_by(Project.id.desc()).limit(5).all())

@app.route('/projects')
def projects():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    
    user_projects = Project.query.filter_by(user_id=session['user_id']).all()
    return render_template('projects.html', projects=user_projects)

@app.route('/add_project', methods=['POST'])
def add_project():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    
    name = request.form.get('name')
    location = request.form.get('location')
    history = request.form.get('history')
    description = request.form.get('description')
    
    try:
        # Convert history string to date object if provided
        history_date = datetime.strptime(history, '%Y-%m-%d').date() if history else None
    except ValueError:
        history_date = None
    
    new_project = Project(
        name=name,
        location=location,
        history=history_date,
        description=description,
        user_id=session['user_id']
    )
    
    db.session.add(new_project)
    db.session.commit()
    
    flash('تم إضافة المشروع بنجاح', 'success')
    return redirect(url_for('projects'))

@app.route('/edit_project', methods=['POST'])
def edit_project():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    
    project_id = request.form.get('id')
    name = request.form.get('name')
    location = request.form.get('location')
    history = request.form.get('history')
    description = request.form.get('description')
    
    project = Project.query.get(project_id)
    
    if not project or project.user_id != session['user_id']:
        flash('المشروع غير موجود أو ليس لديك صلاحية التعديل', 'danger')
        return redirect(url_for('projects'))
    
    try:
        # Convert history string to date object if provided
        history_date = datetime.strptime(history, '%Y-%m-%d').date() if history else None
    except ValueError:
        history_date = None
    
    project.name = name
    project.location = location
    project.history = history_date
    project.description = description
    
    db.session.commit()
    
    flash('تم تحديث المشروع بنجاح', 'success')
    return redirect(url_for('projects'))

@app.route('/delete_project/<int:project_id>', methods=['POST'])
def delete_project(project_id):
    if 'user_id' not in session:
        return redirect(url_for('home'))
    
    project = Project.query.get(project_id)
    
    if not project or project.user_id != session['user_id']:
        flash('المشروع غير موجود أو ليس لديك صلاحية الحذف', 'danger')
        return redirect(url_for('projects'))
    
    db.session.delete(project)
    db.session.commit()
    
    flash('تم حذف المشروع بنجاح', 'success')
    return redirect(url_for('projects'))

@app.route('/drawing')
def drawing():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    return render_template('drawing.html')

@app.route('/quantities')
def quantities():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    return render_template('quantities.html')

@app.route('/3dview')
def three_d_view():
    if 'user_id' not in session:
        return redirect(url_for('home'))
    return render_template('3dview.html')

if __name__ == '__main__':
    app.run(debug=True)