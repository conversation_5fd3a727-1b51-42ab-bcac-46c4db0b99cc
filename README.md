# نظام التصميم المعماري

هذا النظام يُمكّن المهندسين المعماريين من إدارة مشاريعهم وتصميم الخطط الأولية وتحليلها وحساب كمياتها من خلال واجهة ويب تفاعلية باللغة العربية.

## المتطلبات الأساسية
- Python 3.7+
- pip

## إعداد المشروع

1. استنساخ المستودع:
   ```bash
   git clone https://github.com/username/architectural-system.git
   cd architectural-system
   ```

2. تثبيت التبعيات:
   ```bash
   pip install -r requirements.txt
   ```

3. تهيئة قاعدة البيانات:
   ```bash
   python init_db.py
   ```

4. تشغيل التطبيق:
   ```bash
   python app.py
   ```

5. فتح المتصفح والدخول على:
   [http://localhost:5000](http://localhost:5000)

## تسجيل الدخول
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password123`

## الميزات
- لوحة تحكم رئيسية لمشاهدة نظرة عامة على المشاريع
- إدارة المشاريع (اسم المشروع، الموقع، الوصف)
- أدوات رسم هندسية تفاعلية (جدران، أبواب، نوافذ)
- حساب الكميات (مساحة، محيط، تكاليف)
- نظام تسجيل دخول وتصاريح للمستخدمين
- واجهة باللغة العربية (من اليمين لليسار) وخطوط مناسبة

## المكتبات المستخدمة
- Flask: إطار عمل تطبيقات الويب
- SQLAlchemy: ORM لقاعدة البيانات
- Fabric.js: مكتبة الرسم التفاعلي
- Bootstrap 5: واجهة المستخدم
- ReportLab: تصدير إلى PDF
- ezdxf: توليد ملفات AutoCAD

## هيكل الملفات
```
.
├── app.py             # التطبيق الرئيسي
├── models.py          # نماذج قاعدة البيانات
├── init_db.py         # تهيئة قاعدة البيانات
├── requirements.txt   # التبعيات
├── README.md          # هذا الملف
└── templates/         # قوالب HTML
    ├── base.html
    ├── login.html
    ├── dashboard.html
    ├── projects.html
    ├── drawing.html
    ├── quantities.html
    └── 3dview.html
```

## التطوير المستقبلي
- دعم كامل لـ Three.js للعرض ثلاثي الأبعاد
- تصدير تقارير PDF
- توليد ملفات AutoCAD
- دعم مستخدمين متعددين بأدوار مختلفة