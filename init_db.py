from app import app, db
from models import User
from werkzeug.security import generate_password_hash

# Create application context
with app.app_context():
    # Create all tables
    db.create_all()
    
    # Check if sample user already exists
    if not User.query.filter_by(email='<EMAIL>').first():
        # Create sample engineer user
        engineer = User(
            name='مهندس تجريبي',
            email='<EMAIL>',
            password=generate_password_hash('password123'),
            role='engineer'
        )
        db.session.add(engineer)
        db.session.commit()
        print('تم إنشاء مستخدم مهندس تجريبي')
    else:
        print('المستخدم موجود بالفعل')
        
    print('تم تهيئة قاعدة البيانات بنجاح')